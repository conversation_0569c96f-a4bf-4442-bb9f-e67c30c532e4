<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:gravity="center"
    android:padding="8dp"
    android:background="@android:color/transparent">

    <ImageView
        android:id="@+id/app_icon"
        android:layout_width="56dp"
        android:layout_height="56dp"
        android:scaleType="fitCenter"
        android:contentDescription="@null" />

    <TextView
        android:id="@+id/app_label"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="6dp"
        android:textColor="@android:color/white"
        android:textSize="11sp"
        android:maxLines="2"
        android:ellipsize="end"
        android:gravity="center"
        android:shadowColor="#000000"
        android:shadowDx="1"
        android:shadowDy="1"
        android:shadowRadius="2" />

</LinearLayout> 