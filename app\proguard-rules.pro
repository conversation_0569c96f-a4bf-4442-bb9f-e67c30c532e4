# Configuración Proguard ultra-agresiva para máxima optimización
-optimizationpasses 5
-dontusemixedcaseclassnames
-dontskipnonpubliclibraryclasses
-dontpreverify
-verbose
-optimizations !code/simplification/arithmetic,!field/*,!class/merging/*

# Mantener actividad principal
-keep public class * extends android.app.Activity
-keep public class com.devfriend.fasterlauncher.MainActivity

# Eliminar logs en release
-assumenosideeffects class android.util.Log {
    public static *** d(...);
    public static *** v(...);
    public static *** i(...);
    public static *** w(...);
    public static *** e(...);
}

# Optimizar agresivamente
-allowaccessmodification
-repackageclasses ''
-mergeinterfacesaggressively